import { Injectable, inject } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';

export interface Design {
  id: number;
  name: string;
  color: string;
}

export interface Unit {
  id: number;
  name: string;
  qnty: number;
}

export interface MaterialType {
  id: number;
  name: string;
  total_value: number;
  total_profit: number;
}

export interface Material {
  id: number;
  name: string;
  sales_type: 'single' | 'group';
  design: Design;
  type: MaterialType;
  unit: Unit;
  unit_order_price: number;
  opening_stock_quantity: number;
  opening_stock_unit_price: number;
  available_quantity: number;
  stock_unit_price: number;
  total_stock_value: number;
  total_profit: number;
  last_updated: string;
}

export interface MaterialCreateRequest {
  name: string;
  sales_type: 'single' | 'group';
  design_id: number;
  type_id: number;
  unit_id: number;
  unit_order_price: number;
  opening_stock_quantity: number;
  opening_stock_unit_price: number;
}

@Injectable({
  providedIn: 'root'
})
export class MaterialService {
  private http = inject(HttpClient);
  private readonly API_BASE = '/api/materials';

  getMaterials(): Observable<Material[]> {
    return this.http.get<Material[]>(`${this.API_BASE}/`);
  }

  getMaterial(id: number): Observable<Material> {
    return this.http.get<Material>(`${this.API_BASE}/${id}/`);
  }

  createMaterial(material: MaterialCreateRequest): Observable<Material> {
    return this.http.post<Material>(`${this.API_BASE}/`, material);
  }

  updateMaterial(id: number, material: Partial<MaterialCreateRequest>): Observable<Material> {
    return this.http.patch<Material>(`${this.API_BASE}/${id}/`, material);
  }

  deleteMaterial(id: number): Observable<void> {
    return this.http.delete<void>(`${this.API_BASE}/${id}/`);
  }

  // Related entities
  getDesigns(): Observable<Design[]> {
    return this.http.get<Design[]>('/api/designs/');
  }

  getUnits(): Observable<Unit[]> {
    return this.http.get<Unit[]>('/api/units/');
  }

  getMaterialTypes(): Observable<MaterialType[]> {
    return this.http.get<MaterialType[]>('/api/material-types/');
  }

  searchMaterials(query: string): Observable<Material[]> {
    return this.http.get<Material[]>(`${this.API_BASE}/search/?q=${encodeURIComponent(query)}`);
  }
}
