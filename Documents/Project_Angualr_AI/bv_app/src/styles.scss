/* You can add global styles to this file, and also import other style files */
@use '@angular/material' as mat;

// Define the theme using Angular Material v20 theming system
html {
  color-scheme: light dark;
  @include mat.theme((
    color: mat.$azure-palette,
    typography: Roboto,
    density: 0
  ));
}

// Global styles for the admin interface
body {
  margin: 0;
  font-family: <PERSON><PERSON>, "Helvetica Neue", sans-serif;
  background-color: #f5f5f5;
}

// Custom overrides for better admin interface appearance
html {
  @include mat.card-overrides((
    elevated-container-shape: 8px,
  ));

  @include mat.button-overrides((
    container-shape: 6px,
  ));

  @include mat.form-field-overrides((
    container-shape: 6px,
  ));
}

// Responsive utilities
.mobile-hidden {
  @media (max-width: 768px) {
    display: none !important;
  }
}

.desktop-hidden {
  @media (min-width: 769px) {
    display: none !important;
  }
}

// Admin specific styles
.admin-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 16px;
}

.admin-header {
  background: white;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  margin-bottom: 24px;
}

.admin-content {
  background: white;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

// Table responsive styles
.mat-mdc-table {
  @media (max-width: 768px) {
    font-size: 12px;

    .mat-mdc-header-cell,
    .mat-mdc-cell {
      padding: 8px 4px;
    }
  }
}
